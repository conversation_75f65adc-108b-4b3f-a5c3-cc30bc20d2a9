#!/usr/bin/env python3
"""
测试模型加载脚本
"""

import os
import sys
from transformers import BertTokenizer, BertForSequenceClassification

def test_model_loading():
    """测试模型加载"""
    print("开始测试模型加载...")
    
    # 尝试不同的模型路径
    possible_paths = [
        './Module/saved_model',
        '../Module/saved_model',
        'Module/saved_model',
        os.path.join(os.path.dirname(__file__), 'Module', 'saved_model')
    ]
    
    model = None
    tokenizer = None
    successful_path = None
    
    for model_path in possible_paths:
        print(f"尝试路径: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"  路径不存在: {model_path}")
            continue
            
        try:
            print(f"  正在加载模型...")
            model = BertForSequenceClassification.from_pretrained(
                model_path, 
                local_files_only=True
            )
            tokenizer = BertTokenizer.from_pretrained(
                model_path, 
                local_files_only=True
            )
            successful_path = model_path
            print(f"  ✅ 成功加载模型从: {model_path}")
            break
            
        except Exception as e:
            print(f"  ❌ 加载失败: {str(e)}")
            continue
    
    if model is None or tokenizer is None:
        print("❌ 所有路径都无法加载模型")
        return False
    
    # 测试预测
    print("\n开始测试预测...")
    test_text = "应用经常卡顿，使用体验很差"
    
    try:
        inputs = tokenizer.encode_plus(
            test_text,
            add_special_tokens=True,
            max_length=128,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        import torch
        with torch.no_grad():
            outputs = model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=1)
            predicted_class = torch.argmax(predictions, dim=1).item()
            confidence = torch.max(predictions, dim=1)[0].item()
        
        print(f"测试文本: {test_text}")
        print(f"预测类别: {predicted_class}")
        print(f"置信度: {confidence:.4f}")
        print("✅ 预测测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n🎉 模型加载和预测测试全部通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)

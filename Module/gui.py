from Module.utils import LabelConverter

class TextClassificationUI(QMainWindow):
    def classify_text(self):
        try:
            # 更新结果显示，添加标签含义
            predicted_label = LabelConverter.id_to_label(predicted_class)
            self.result_label.setText(f'预测类别: {predicted_label} (ID: {predicted_class})')
            
        except Exception as e:
            self.result_label.setText(f'分类过程出错: {str(e)}') 
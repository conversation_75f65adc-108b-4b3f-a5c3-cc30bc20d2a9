class LabelConverter:
    @staticmethod
    def load_label_mapping():
        """加载标签映射关系"""
        label_to_id = {}
        try:
            with open('./label_mapping.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()[1:]  # 跳过第一行说明文字
                for line in lines:
                    label, id_str = line.strip().split(' -> ')
                    label = label.replace('标签: ', '')
                    id_num = int(id_str.replace('ID: ', ''))
                    label_to_id[label] = id_num
        except FileNotFoundError:
            print("警告：找不到标签映射文件")
            return {}
        return label_to_id

    @staticmethod
    def id_to_label(label_id):
        """将数字ID转换回原始标签"""
        label_to_id = LabelConverter.load_label_mapping()
        id_to_label = {v: k for k, v in label_to_id.items()}
        return id_to_label.get(label_id, f"未知类别({label_id})") 
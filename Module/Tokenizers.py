import pandas as pd
from tokenizers import Tokenizer
from tokenizers.models import BPE
from tokenizers.trainers import BpeTrainer
from tokenizers.pre_tokenizers import ByteLevel
from tokenizers.processors import TemplateProcessing

# 1. 初始化 BBPE 分词器
tokenizer = Tokenizer(BPE(unk_token="[UNK]"))  # 使用 BPE 模型，设置未知词标记

# 2. 设置字节级别的预分词器
tokenizer.pre_tokenizer = ByteLevel()

# 3. 定义训练器
trainer = BpeTrainer(
    vocab_size=30000,  # 词汇表大小
    special_tokens=["[PAD]", "[UNK]", "[CLS]", "[SEP]", "[MASK]"],  # 特殊标记
    min_frequency=2,  # 最小词频
    show_progress=True  # 显示训练进度
)

# 4. 准备训练数据
# 这里我们使用一个简单的文本文件作为训练数据
# 你可以替换为自己的文本文件路径
training_data = ["../dataSet/train_jichu_2ji_0914.txt"]  # 假设你的文本数据在 data.txt 中

# 5. 训练分词器
tokenizer.train(training_data, trainer)

# 6. 保存分词器和词汇表
tokenizer.save("bbpe_tokenizer.json")  # 保存分词器
print("分词器和词汇表已保存到 bbpe_tokenizer.json")

# 7. 加载分词器
tokenizer = Tokenizer.from_file("bbpe_tokenizer.json")

# 8. 设置后处理模板（可选）
# 例如，为输入添加 [CLS] 和 [SEP] 标记
tokenizer.post_processor = TemplateProcessing(
    single="[CLS] $A [SEP]",
    pair="[CLS] $A [SEP] $B:1 [SEP]:1",
    special_tokens=[
        ("[CLS]", tokenizer.token_to_id("[CLS]")),
        ("[SEP]", tokenizer.token_to_id("[SEP]")),
    ],
)

# 9. 使用分词器对句子进行分词
sentence = "Hello, 世界！这是一个BBPE分词器的测试。"
encoding = tokenizer.encode(sentence)

# 输出分词结果
print("原始句子:", sentence)
print("分词结果:", encoding.tokens)
print("Token IDs:", encoding.ids)
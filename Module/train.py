import torch
from torch.utils.data import DataLoader, Dataset
from transformers import Bert<PERSON>oken<PERSON>, BertForSequenceClassification, AdamW
import pandas as pd
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os


# 需要注意，这个训练代码环境是MacOS使用 apple 为 m系列芯片 设计的 mps 训练加速，
# 如果是 Windows 系统 英伟达显卡 需要修改为 CUDA 训练
class TextDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_len = max_len

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]

        encoding = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=self.max_len,
            return_token_type_ids=False,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt',
        )

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }


def plot_loss(losses, save_path='training_loss.png'):
    plt.figure(figsize=(10, 6))
    plt.plot(losses, label='训练损失')
    plt.title('训练过程中的损失变化')
    plt.xlabel('步数')
    plt.ylabel('损失')
    plt.legend()
    plt.grid(True)
    plt.savefig(save_path)
    plt.close()


def train_model(train_data_path, model_save_path='./saved_model'):
    # 检查是否可以使用 MPS 加速
    if torch.backends.mps.is_available():
        device = torch.device("mps")
        print("使用 MPS 设备加速训练")
    else:
        device = torch.device("cpu")
        print("使用 CPU 训练")

    # 添加文件检查
    if not os.path.exists(train_data_path):
        raise FileNotFoundError(f"训练数据文件不存在: {train_data_path}")

    print(f"使用设备: {device}")

    try:
        # 加载数据
        df = pd.read_csv(train_data_path)
    except Exception as e:
        print(f"加载数据时出错: {str(e)}")
        return

    texts = df['text'].values
    labels = df['label_encoded'].values

    # 初始化tokenizer
    tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')

    # 创建数据集 - 增加批量大小
    max_len = 128
    train_dataset = TextDataset(texts, labels, tokenizer, max_len)
    train_loader = DataLoader(
        train_dataset,
        batch_size=24,  # M1 适配的批量大小
        shuffle=True,
        num_workers=2,  # M1 芯片推荐的工作进程数
        pin_memory=True
    )

    # 初始化模型
    num_labels = len(np.unique(labels))

    # 检查是否存在已保存的模型
    if os.path.exists(model_save_path):
        print("检测到已保存的模型，加载模型继续训练...")
        model = BertForSequenceClassification.from_pretrained(model_save_path)
    else:
        print("未检测到已保存的模型，初始化新模型...")
        model = BertForSequenceClassification.from_pretrained(
            'bert-base-chinese',
            num_labels=num_labels
        )

    model = model.to(device)

    # 设置优化器和调度器
    optimizer = AdamW(model.parameters(), lr=2e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.1, patience=2, verbose=True
    )

    # 添加混合精度训练
    scaler = torch.cuda.amp.GradScaler()

    # 添加早停
    best_loss = float('inf')
    patience = 3
    patience_counter = 0

    # 训练参数
    epochs = 5
    losses = []
    accumulation_steps = 2  # 梯度累积步数

    # 训练循环
    for epoch in range(epochs):
        print(f"\n开始 Epoch {epoch + 1}/{epochs}")

        model.train()
        epoch_losses = []
        optimizer.zero_grad()

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f"Epoch {epoch + 1}训练中")):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )

            loss = outputs.loss / accumulation_steps
            loss.backward()

            if (batch_idx + 1) % accumulation_steps == 0:
                optimizer.step()
                optimizer.zero_grad()

            epoch_losses.append(loss.item() * accumulation_steps)
            losses.append(loss.item() * accumulation_steps)

        avg_epoch_loss = sum(epoch_losses) / len(epoch_losses)
        print(f"Epoch {epoch + 1} 平均损失: {avg_epoch_loss:.4f}")

        # 更新学习率
        scheduler.step(avg_epoch_loss)

        # 早停检查
        if avg_epoch_loss < best_loss:
            best_loss = avg_epoch_loss
            patience_counter = 0
            model.save_pretrained(model_save_path + '_best')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print("触发早停机制")
                break

    # 保存模型
    print("保存模型...")
    model.save_pretrained(model_save_path)
    tokenizer.save_pretrained(model_save_path)

    # 绘制损失曲线
    plot_loss(losses)
    print("训练损失曲线已保存为 training_loss.png")


if __name__ == "__main__":
    train_data_path = "../dataSet/train_data_processed.csv"
    train_model(train_data_path)

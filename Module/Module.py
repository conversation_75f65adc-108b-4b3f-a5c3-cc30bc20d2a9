import torch
from torch import nn
from transformers import Bert<PERSON>okenizer, BertForSequenceClassification, AdamW
from torch.utils.data import DataLoader, Dataset
import pandas as pd

# 自定义数据集
class TextDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_len = max_len

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]
        encoding = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=self.max_len,
            return_token_type_ids=False,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt',
        )
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

# 读取CSV数据
def load_data(csv_file):
    df = pd.read_csv(csv_file)
    texts = df['text'].tolist()
    labels = df['label'].tolist()
    return texts, labels


def test(csv_file):
    # 加载数据
    texts, labels = load_data(csv_file)

    # 标签编码
    label_to_id = {label: idx for idx, label in enumerate(sorted(set(labels)))}
    id_to_label = {idx: label for label, idx in label_to_id.items()}
    
    # 保存标签映射关系
    with open('./label_mapping.txt', 'w', encoding='utf-8') as f:
        f.write("标签映射关系：\n")
        for label, idx in label_to_id.items():
            f.write(f"标签: {label} -> ID: {idx}\n")
    
    # 转换标签为数字
    labels = [label_to_id[label] for label in labels]
    
    # 打印统计信息
    print("标签映射关系：")
    for label, idx in label_to_id.items():
        print(f"标签: {label} -> ID: {idx}")
    print("\n标签分布：")
    from collections import Counter
    label_counts = Counter(labels)
    for label_id, count in label_counts.items():
        print(f"类别 {id_to_label[label_id]} (ID: {label_id}): {count} 条数据")
    
    return texts, labels, label_to_id

# 主训练函数
def train_model(csv_file):
    # 加载数据
    texts, labels = load_data(csv_file)

    # 标签编码
    label_to_id = {label: idx for idx, label in enumerate(set(labels))}
    labels = [label_to_id[label] for label in labels]

    # 初始化tokenizer和数据集
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    max_len = 128
    dataset = TextDataset(texts, labels, tokenizer, max_len)
    dataloader = DataLoader(dataset, batch_size=8, shuffle=True)

    # 加载BERT模型
    model = BertForSequenceClassification.from_pretrained('bert-base-uncased', num_labels=len(label_to_id))
    optimizer = AdamW(model.parameters(), lr=1e-5)

    # 训练模型
    model.train()
    for epoch in range(3):  # 训练3个epoch
        for batch in dataloader:
            optimizer.zero_grad()
            input_ids = batch['input_ids']
            attention_mask = batch['attention_mask']
            labels = batch['labels']
            outputs = model(input_ids, attention_mask=attention_mask, labels=labels)
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            print(f"Epoch {epoch + 1}, 损失: {loss.item()}")

    # 保存模型
    model.save_pretrained('./bert-text-classification')
    tokenizer.save_pretrained('./bert-text-classification')

# 使用示例


if __name__ == '__main__':
    # test.txt('../dataSet/test_data.csv')
    csv_file = '../dataSet/test_data.csv'  # 替换为你的CSV文件路径
    train_model(csv_file)
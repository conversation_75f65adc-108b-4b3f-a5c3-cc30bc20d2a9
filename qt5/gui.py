import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QTextEdit, QPushButton, QLabel, QListWidget, QHBoxLayout, QFileDialog, QGroupBox, QFrame, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt
import torch
from transformers import BertTokenizer, BertForSequenceClassification

class TextClassificationUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('文本分类系统')
        self.setGeometry(100, 100, 800, 600)  # 增大窗口
        
        # 创建标签映射字典
        self.label_map = {
            0: "其他", 1: "卡顿", 2: "发布不显示", 3: "发布信息缺失",
            4: "发布繁琐", 5: "定位", 6: "微聊沟通", 7: "找不到发布入口",
            8: "推送", 9: "提现", 10: "搜索", 11: "收藏",
            12: "无法创建", 13: "无法发布", 14: "无法定位", 15: "无法投递",
            16: "梦想小镇", 17: "流程错误", 18: "界面不清晰", 19: "筛选",
            20: "签到", 21: "编辑", 22: "自动回复", 23: "自动续费",
            24: "视频任务", 25: "闪退", 26: "页面打不开"
        }
        
        # 加载模型和tokenizer
        try:
            self.model = BertForSequenceClassification.from_pretrained('../Module/saved_model')
            self.tokenizer = BertTokenizer.from_pretrained('../Module/saved_model')
            self.model.eval()  # 设置为评估模式
        except Exception as e:
            print(f"模型加载错误: {e}")
            self.model = None
            self.tokenizer = None

        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # --- 类别展示区 ---
        category_group = QGroupBox("系统可识别类别")
        grid_layout = QGridLayout()
        col_num = 4  # 每行显示4个类别
        for idx, (k, v) in enumerate(self.label_map.items()):
            label = QLabel(f"{k}: {v}")
            label.setStyleSheet("padding: 2px 8px; border-radius: 4px; background: #f0f0f0;")
            label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            row = idx // col_num
            col = idx % col_num
            grid_layout.addWidget(label, row, col)
        category_group.setLayout(grid_layout)
        main_layout.addWidget(category_group)
        
        # 分隔线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line1)
        
        # --- 输入与按钮区 ---
        input_group = QGroupBox("文本输入与操作")
        input_layout = QVBoxLayout()
        
        instruction_label = QLabel('请输入要分类的文本：')
        input_layout.addWidget(instruction_label)
        
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText('在此输入文本...')
        input_layout.addWidget(self.text_input)
        
        btn_layout = QHBoxLayout()
        self.submit_button = QPushButton('开始分类')
        self.submit_button.clicked.connect(self.classify_text)
        btn_layout.addWidget(self.submit_button)
        
        self.import_button = QPushButton('导入文件分类')
        self.import_button.clicked.connect(self.import_file_and_classify)
        btn_layout.addWidget(self.import_button)
        
        input_layout.addLayout(btn_layout)
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)
        
        # --- 结果区 ---
        result_group = QGroupBox("分类结果")
        result_layout = QVBoxLayout()
        self.result_label = QLabel('分类结果将在这里显示')
        self.result_label.setAlignment(Qt.AlignCenter)
        result_layout.addWidget(self.result_label)
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)
        
        # --- 历史与文件结果区 ---
        history_group = QGroupBox("历史记录与文件分类结果")
        history_layout = QHBoxLayout()
        
        self.history_list = QListWidget()
        self.history_list.setMaximumHeight(120)
        self.history_list.itemClicked.connect(self.fill_from_history)
        history_layout.addWidget(self.history_list)
        
        self.clear_history_button = QPushButton('清空历史')
        self.clear_history_button.clicked.connect(self.clear_history)
        history_layout.addWidget(self.clear_history_button)
        
        # 新增：文件分类结果展示
        self.file_result_list = QListWidget()
        self.file_result_list.setMaximumHeight(120)
        history_layout.addWidget(self.file_result_list)
        
        history_group.setLayout(history_layout)
        main_layout.addWidget(history_group)
        
        # 历史数据
        self.history = []
        
    def classify_text(self):
        if self.model is None or self.tokenizer is None:
            self.result_label.setText('错误：模型未正确加载')
            return
            
        text = self.text_input.toPlainText().strip()
        if not text:
            self.result_label.setText('请输入文本')
            return
            
        try:
            predicted_label = self._predict_label(text)
            self.result_label.setText(f'预测类别: {predicted_label}')
            record = f"文本: {text} | 预测: {predicted_label}"
            self.history.append((text, predicted_label))
            self.history_list.addItem(record)
        except Exception as e:
            self.result_label.setText(f'分类过程出错: {str(e)}')
    
    def _predict_label(self, text):
        inputs = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=128,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=1)
            predicted_class = torch.argmax(predictions, dim=1).item()
        predicted_label = self.label_map.get(predicted_class, "未知类别")
        return predicted_label
    
    def import_file_and_classify(self):
        if self.model is None or self.tokenizer is None:
            self.result_label.setText('错误：模型未正确加载')
            return
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文本文件", "", "Text Files (*.txt)")
        if not file_path:
            return
        try:
            self.file_result_list.clear()
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]
            for idx, line in enumerate(lines):
                label = self._predict_label(line)
                result = f"第{idx+1}行: {line} | 预测: {label}"
                self.file_result_list.addItem(result)
            self.result_label.setText(f"文件分类完成，共{len(lines)}行。")
        except Exception as e:
            self.result_label.setText(f'文件分类出错: {str(e)}')
    
    def fill_from_history(self, item):
        text = item.text().split('|')[0].replace('文本: ', '').strip()
        self.text_input.setText(text)
    
    def clear_history(self):
        self.history.clear()
        self.history_list.clear()

def main():
    app = QApplication(sys.argv)
    window = TextClassificationUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
import sys
import os
import hashlib
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QTextEdit, QPushButton, QLabel, QListWidget, QHBoxLayout,
                            QFileDialog, QGroupBox, QFrame, QGridLayout, QSizePolicy,
                            QLineEdit, QCheckBox, QMessageBox, QDialog, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QProgressBar, QFormLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings
import torch
from transformers import BertTokenizer, BertForSequenceClassification
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import pandas as pd
import numpy as np

# 尝试导入seaborn，如果失败则使用matplotlib
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

class LoginDialog(QDialog):
    """登录对话框"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle('用户登录 - 文本分类系统')
        self.setFixedSize(400, 300)
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
            QLineEdit {
                padding: 15px 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                font-size: 16px;
                background: rgba(255, 255, 255, 0.95);
                color: #333;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background: white;
                box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: #3d8b40;
                transform: translateY(0px);
            }
            QCheckBox {
                color: white;
                font-size: 14px;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid rgba(255, 255, 255, 0.7);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: #4CAF50;
                border-color: #4CAF50;
            }
        """)

        self.setup_ui()
        self.load_saved_credentials()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)

        # 标题
        title_label = QLabel('🤖 智能文本分类系统')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)
        layout.addWidget(title_label)

        # 用户名输入
        username_label = QLabel('用户名:')
        layout.addWidget(username_label)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('请输入用户名')
        layout.addWidget(self.username_input)

        # 密码输入
        password_label = QLabel('密码:')
        layout.addWidget(password_label)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('请输入密码')
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_input)

        # 记住密码选项
        self.remember_checkbox = QCheckBox('记住登录信息')
        layout.addWidget(self.remember_checkbox)

        # 登录按钮
        login_button = QPushButton('登录')
        login_button.clicked.connect(self.login)
        layout.addWidget(login_button)

        # 绑定回车键登录
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)

        self.setLayout(layout)

    def login(self):
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            QMessageBox.warning(self, '警告', '请输入用户名和密码！')
            return

        # 简单的用户验证（实际项目中应该连接数据库）
        if self.validate_user(username, password):
            if self.remember_checkbox.isChecked():
                self.save_credentials(username, password)
            else:
                self.clear_saved_credentials()
            self.accept()
        else:
            QMessageBox.critical(self, '错误', '用户名或密码错误！')

    def validate_user(self, username, password):
        """验证用户凭据"""
        # 默认用户：admin/admin, user/123456
        valid_users = {
            'admin': 'admin',
            'user': '123456',
            'demo': 'demo'
        }
        return username in valid_users and valid_users[username] == password

    def save_credentials(self, username, password):
        """保存登录凭据"""
        settings = QSettings('TextClassification', 'LoginInfo')
        settings.setValue('username', username)
        # 简单加密密码（实际项目中应使用更安全的方法）
        encrypted_password = hashlib.md5(password.encode()).hexdigest()
        settings.setValue('password', encrypted_password)
        settings.setValue('remember', True)

    def load_saved_credentials(self):
        """加载保存的登录凭据"""
        settings = QSettings('TextClassification', 'LoginInfo')
        if settings.value('remember', False, type=bool):
            username = settings.value('username', '')
            self.username_input.setText(username)
            self.remember_checkbox.setChecked(True)

    def clear_saved_credentials(self):
        """清除保存的登录凭据"""
        settings = QSettings('TextClassification', 'LoginInfo')
        settings.clear()

class TextClassificationUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('智能文本分类系统 v2.0')
        self.setGeometry(100, 100, 1200, 800)  # 增大窗口

        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 2px solid rgba(0, 0, 0, 0.1);
                border-radius: 15px;
                margin-top: 1ex;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(255, 255, 255, 0.85));
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 10px;
                font-size: 15px;
                font-weight: bold;
                min-width: 130px;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            }
            QPushButton:pressed {
                background: #3d8b40;
                transform: translateY(0px);
            }
            QTextEdit {
                border: 2px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                padding: 15px;
                font-size: 15px;
                background: white;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
                box-shadow: 0 0 15px rgba(76, 175, 80, 0.2);
            }
            QListWidget {
                border: 2px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: white;
                alternate-background-color: #f8f9fa;
                font-size: 14px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-radius: 6px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background: #e3f2fd;
            }
            QListWidget::item:selected {
                background: #4CAF50;
                color: white;
            }
            QLabel {
                color: #2c3e50;
                font-size: 15px;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
            QTabWidget::pane {
                border: 2px solid rgba(0, 0, 0, 0.1);
                background: white;
                border-radius: 15px;
                margin-top: 5px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e9ecef, stop:1 #dee2e6);
                padding: 12px 20px;
                margin-right: 3px;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #495057;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
        """)

        # 用户信息
        self.current_user = None
        self.classification_history = []

        # 创建标签映射字典
        self.label_map = {
            0: "其他", 1: "卡顿", 2: "发布不显示", 3: "发布信息缺失",
            4: "发布繁琐", 5: "定位", 6: "微聊沟通", 7: "找不到发布入口",
            8: "推送", 9: "提现", 10: "搜索", 11: "收藏",
            12: "无法创建", 13: "无法发布", 14: "无法定位", 15: "无法投递",
            16: "梦想小镇", 17: "流程错误", 18: "界面不清晰", 19: "筛选",
            20: "签到", 21: "编辑", 22: "自动回复", 23: "自动续费",
            24: "视频任务", 25: "闪退", 26: "页面打不开"
        }

        # 显示登录对话框
        if not self.show_login():
            sys.exit()

        # 初始化UI
        self.init_ui()

        # 加载模型
        self.load_model()

    def show_login(self):
        """显示登录对话框"""
        login_dialog = LoginDialog()
        if login_dialog.exec_() == QDialog.Accepted:
            self.current_user = login_dialog.username_input.text()
            return True
        return False

    def init_ui(self):
        """初始化用户界面"""
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        central_widget_layout = QVBoxLayout(central_widget)

        # 添加用户信息栏
        self.create_user_info_bar(central_widget_layout)

        central_widget_layout.addWidget(self.tab_widget)

        # 创建各个选项卡
        self.create_classification_tab()
        self.create_analysis_tab()
        self.create_batch_processing_tab()
        self.create_settings_tab()

    def create_user_info_bar(self, parent_layout):
        """创建用户信息栏"""
        user_info_frame = QFrame()
        user_info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            }
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 16px;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            }
        """)
        user_info_frame.setFixedHeight(60)

        user_layout = QHBoxLayout(user_info_frame)

        # 欢迎信息
        welcome_label = QLabel(f'👋 欢迎使用，{self.current_user}！')
        welcome_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        user_layout.addWidget(welcome_label)

        user_layout.addStretch()

        # 当前时间
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 14px; color: rgba(255, 255, 255, 0.9);")
        self.update_time()
        user_layout.addWidget(self.time_label)

        # 设置定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新

        # 退出按钮
        logout_button = QPushButton('🚪 退出登录')
        logout_button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.7);
                color: white;
                padding: 8px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: white;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """)
        logout_button.clicked.connect(self.logout)
        user_layout.addWidget(logout_button)

        parent_layout.addWidget(user_info_frame)

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.setText(f'🕐 {current_time}')

    def logout(self):
        """退出登录"""
        reply = QMessageBox.question(self, '确认', '确定要退出登录吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close()

    def load_model(self):
        """加载BERT模型"""
        try:
            # 使用绝对路径或相对路径
            model_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'Module', 'saved_model')

            # 检查模型路径是否存在
            if not os.path.exists(model_path):
                # 尝试其他可能的路径
                alternative_paths = [
                    './Module/saved_model',
                    '../Module/saved_model',
                    'Module/saved_model'
                ]

                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        model_path = alt_path
                        break
                else:
                    raise FileNotFoundError(f"找不到模型文件，尝试的路径: {alternative_paths}")

            print(f"正在从路径加载模型: {model_path}")

            # 使用local_files_only=True来强制使用本地文件
            self.model = BertForSequenceClassification.from_pretrained(
                model_path,
                local_files_only=True
            )
            self.tokenizer = BertTokenizer.from_pretrained(
                model_path,
                local_files_only=True
            )
            self.model.eval()  # 设置为评估模式
            QMessageBox.information(self, '成功', '模型加载成功！')

        except Exception as e:
            error_msg = f"模型加载错误: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, '错误', error_msg)
            self.model = None
            self.tokenizer = None

    def create_classification_tab(self):
        """创建文本分类选项卡"""
        classification_widget = QWidget()
        main_layout = QVBoxLayout(classification_widget)

        # --- 类别展示区 ---
        category_group = QGroupBox("🏷️ 系统可识别类别")
        grid_layout = QGridLayout()
        grid_layout.setSpacing(10)
        col_num = 4  # 每行显示4个类别

        # 定义颜色主题
        colors = [
            ("#e8f5e8", "#4caf50", "#2e7d32"),  # 绿色
            ("#e3f2fd", "#2196f3", "#1565c0"),  # 蓝色
            ("#fff3e0", "#ff9800", "#ef6c00"),  # 橙色
            ("#fce4ec", "#e91e63", "#ad1457"),  # 粉色
            ("#f3e5f5", "#9c27b0", "#6a1b9a"),  # 紫色
            ("#e0f2f1", "#009688", "#00695c"),  # 青色
        ]

        for idx, (k, v) in enumerate(self.label_map.items()):
            color_theme = colors[idx % len(colors)]
            label = QLabel(f"{k}: {v}")
            label.setStyleSheet(f"""
                padding: 12px 16px;
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color_theme[0]}, stop:1 rgba(255, 255, 255, 0.8));
                border: 2px solid {color_theme[1]};
                color: {color_theme[2]};
                font-weight: bold;
                font-size: 13px;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            """)
            label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            row = idx // col_num
            col = idx % col_num
            grid_layout.addWidget(label, row, col)
        category_group.setLayout(grid_layout)
        main_layout.addWidget(category_group)

        # --- 输入与按钮区 ---
        input_group = QGroupBox("✏️ 文本输入与操作")
        input_layout = QVBoxLayout()
        input_layout.setSpacing(15)

        instruction_label = QLabel('📝 请输入要分类的文本：')
        instruction_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        input_layout.addWidget(instruction_label)

        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText('在此输入您要分类的文本内容...')
        self.text_input.setMinimumHeight(120)
        self.text_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                padding: 15px;
                font-size: 15px;
                background: white;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                line-height: 1.6;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
                box-shadow: 0 0 20px rgba(76, 175, 80, 0.2);
            }
        """)
        input_layout.addWidget(self.text_input)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(15)

        self.submit_button = QPushButton('🔍 开始分类')
        self.submit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            }
        """)
        self.submit_button.clicked.connect(self.classify_text)
        btn_layout.addWidget(self.submit_button)

        self.import_button = QPushButton('📁 导入文件分类')
        self.import_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
            }
        """)
        self.import_button.clicked.connect(self.import_file_and_classify)
        btn_layout.addWidget(self.import_button)

        self.clear_input_button = QPushButton('🗑️ 清空输入')
        self.clear_input_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF9800, stop:1 #F57C00);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F57C00, stop:1 #EF6C00);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
            }
        """)
        self.clear_input_button.clicked.connect(lambda: self.text_input.clear())
        btn_layout.addWidget(self.clear_input_button)

        input_layout.addLayout(btn_layout)
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # --- 结果区 ---
        result_group = QGroupBox("分类结果")
        result_layout = QVBoxLayout()
        self.result_label = QLabel('分类结果将在这里显示')
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2e7d32;
            padding: 20px;
            background: #f1f8e9;
            border-radius: 8px;
            border: 2px solid #c8e6c9;
        """)
        result_layout.addWidget(self.result_label)
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)

        # --- 历史与文件结果区 ---
        history_group = QGroupBox("历史记录与文件分类结果")
        history_layout = QHBoxLayout()

        # 历史记录部分
        history_left_layout = QVBoxLayout()
        history_label = QLabel('分类历史:')
        history_label.setStyleSheet("font-weight: bold;")
        history_left_layout.addWidget(history_label)

        self.history_list = QListWidget()
        self.history_list.setMaximumHeight(150)
        self.history_list.itemClicked.connect(self.fill_from_history)
        history_left_layout.addWidget(self.history_list)

        self.clear_history_button = QPushButton('清空历史')
        self.clear_history_button.clicked.connect(self.clear_history)
        history_left_layout.addWidget(self.clear_history_button)

        history_layout.addLayout(history_left_layout)

        # 文件结果部分
        file_result_layout = QVBoxLayout()
        file_result_label = QLabel('文件分类结果:')
        file_result_label.setStyleSheet("font-weight: bold;")
        file_result_layout.addWidget(file_result_label)

        self.file_result_list = QListWidget()
        self.file_result_list.setMaximumHeight(150)
        file_result_layout.addWidget(self.file_result_list)

        export_button = QPushButton('导出结果')
        export_button.clicked.connect(self.export_results)
        file_result_layout.addWidget(export_button)

        history_layout.addLayout(file_result_layout)

        history_group.setLayout(history_layout)
        main_layout.addWidget(history_group)

        # 添加到选项卡
        self.tab_widget.addTab(classification_widget, "📝 文本分类")

        # 历史数据
        self.history = []

    def create_analysis_tab(self):
        """创建数据分析选项卡"""
        analysis_widget = QWidget()
        layout = QVBoxLayout(analysis_widget)

        # 分析控制面板
        control_group = QGroupBox("分析控制面板")
        control_layout = QHBoxLayout()

        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "分类结果统计", "历史趋势分析", "准确率分析", "类别分布图"
        ])
        control_layout.addWidget(QLabel("分析类型:"))
        control_layout.addWidget(self.analysis_type_combo)

        self.generate_chart_button = QPushButton("📊 生成图表")
        self.generate_chart_button.clicked.connect(self.generate_analysis_chart)
        control_layout.addWidget(self.generate_chart_button)

        self.save_chart_button = QPushButton("💾 保存图表")
        self.save_chart_button.clicked.connect(self.save_chart)
        control_layout.addWidget(self.save_chart_button)

        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 图表显示区域
        self.chart_widget = QWidget()
        self.chart_layout = QVBoxLayout(self.chart_widget)

        # 创建matplotlib图表
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        self.chart_layout.addWidget(self.canvas)

        layout.addWidget(self.chart_widget)

        # 统计信息显示
        stats_group = QGroupBox("统计信息")
        self.stats_table = QTableWidget()
        self.stats_table.setMaximumHeight(200)
        stats_layout = QVBoxLayout()
        stats_layout.addWidget(self.stats_table)
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        self.tab_widget.addTab(analysis_widget, "📊 数据分析")

    def create_batch_processing_tab(self):
        """创建批量处理选项卡"""
        batch_widget = QWidget()
        layout = QVBoxLayout(batch_widget)

        # 文件选择区域
        file_group = QGroupBox("批量文件处理")
        file_layout = QVBoxLayout()

        file_select_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("padding: 8px; background: #f5f5f5; border-radius: 4px;")
        file_select_layout.addWidget(self.file_path_label)

        select_file_button = QPushButton("选择文件")
        select_file_button.clicked.connect(self.select_batch_file)
        file_select_layout.addWidget(select_file_button)

        file_layout.addLayout(file_select_layout)

        # 处理选项
        options_layout = QHBoxLayout()

        self.include_confidence = QCheckBox("包含置信度")
        self.include_confidence.setChecked(True)
        options_layout.addWidget(self.include_confidence)

        self.save_detailed_results = QCheckBox("保存详细结果")
        self.save_detailed_results.setChecked(True)
        options_layout.addWidget(self.save_detailed_results)

        options_layout.addStretch()

        file_layout.addLayout(options_layout)

        # 处理按钮
        process_layout = QHBoxLayout()
        self.batch_process_button = QPushButton("🚀 开始批量处理")
        self.batch_process_button.clicked.connect(self.start_batch_processing)
        process_layout.addWidget(self.batch_process_button)

        self.stop_process_button = QPushButton("⏹️ 停止处理")
        self.stop_process_button.clicked.connect(self.stop_batch_processing)
        self.stop_process_button.setEnabled(False)
        process_layout.addWidget(self.stop_process_button)

        file_layout.addLayout(process_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        file_layout.addWidget(self.progress_bar)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 结果显示区域
        result_group = QGroupBox("处理结果")
        result_layout = QVBoxLayout()

        self.batch_result_table = QTableWidget()
        self.batch_result_table.setColumnCount(4)
        self.batch_result_table.setHorizontalHeaderLabels(["序号", "文本内容", "预测类别", "置信度"])
        header = self.batch_result_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        result_layout.addWidget(self.batch_result_table)

        # 导出按钮
        export_layout = QHBoxLayout()
        export_csv_button = QPushButton("导出CSV")
        export_csv_button.clicked.connect(lambda: self.export_batch_results('csv'))
        export_layout.addWidget(export_csv_button)

        export_excel_button = QPushButton("导出Excel")
        export_excel_button.clicked.connect(lambda: self.export_batch_results('excel'))
        export_layout.addWidget(export_excel_button)

        export_layout.addStretch()
        result_layout.addLayout(export_layout)

        result_group.setLayout(result_layout)
        layout.addWidget(result_group)

        self.tab_widget.addTab(batch_widget, "⚡ 批量处理")

    def create_settings_tab(self):
        """创建设置选项卡"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # 模型设置
        model_group = QGroupBox("模型设置")
        model_layout = QFormLayout()

        # 设置默认模型路径
        default_model_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'Module', 'saved_model')
        if not os.path.exists(default_model_path):
            default_model_path = "./Module/saved_model"
        self.model_path_input = QLineEdit(default_model_path)
        model_layout.addRow("模型路径:", self.model_path_input)

        self.max_length_input = QLineEdit("128")
        model_layout.addRow("最大序列长度:", self.max_length_input)

        reload_model_button = QPushButton("重新加载模型")
        reload_model_button.clicked.connect(self.reload_model)
        model_layout.addRow("", reload_model_button)

        model_group.setLayout(model_layout)
        layout.addWidget(model_group)

        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout()

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认主题", "深色主题", "蓝色主题"])
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        ui_layout.addRow("主题:", self.theme_combo)

        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["小", "中", "大"])
        self.font_size_combo.setCurrentText("中")
        ui_layout.addRow("字体大小:", self.font_size_combo)

        ui_group.setLayout(ui_layout)
        layout.addWidget(ui_group)

        # 用户管理
        user_group = QGroupBox("用户管理")
        user_layout = QVBoxLayout()

        current_user_label = QLabel(f"当前用户: {self.current_user}")
        current_user_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        user_layout.addWidget(current_user_label)

        user_buttons_layout = QHBoxLayout()
        change_password_button = QPushButton("修改密码")
        change_password_button.clicked.connect(self.change_password)
        user_buttons_layout.addWidget(change_password_button)

        view_users_button = QPushButton("用户管理")
        view_users_button.clicked.connect(self.manage_users)
        user_buttons_layout.addWidget(view_users_button)

        user_buttons_layout.addStretch()
        user_layout.addLayout(user_buttons_layout)

        user_group.setLayout(user_layout)
        layout.addWidget(user_group)

        layout.addStretch()

        self.tab_widget.addTab(settings_widget, "⚙️ 设置")

    def classify_text(self):
        if self.model is None or self.tokenizer is None:
            self.result_label.setText('❌ 错误：模型未正确加载')
            return

        text = self.text_input.toPlainText().strip()
        if not text:
            self.result_label.setText('⚠️ 请输入文本')
            return

        try:
            predicted_label, confidence = self._predict_label_with_confidence(text)
            self.result_label.setText(f'✅ 预测类别: {predicted_label} (置信度: {confidence:.2%})')

            # 记录到历史
            timestamp = datetime.now().strftime('%H:%M:%S')
            record = f"[{timestamp}] {text[:50]}{'...' if len(text) > 50 else ''} → {predicted_label}"
            self.history.append({
                'text': text,
                'label': predicted_label,
                'confidence': confidence,
                'timestamp': timestamp
            })
            self.history_list.addItem(record)

            # 记录到分类历史用于分析
            self.classification_history.append({
                'text': text,
                'predicted_label': predicted_label,
                'confidence': confidence,
                'timestamp': datetime.now(),
                'user': self.current_user
            })

        except Exception as e:
            self.result_label.setText(f'❌ 分类过程出错: {str(e)}')
    
    def _predict_label(self, text):
        """原始预测方法，保持兼容性"""
        predicted_label, _ = self._predict_label_with_confidence(text)
        return predicted_label

    def _predict_label_with_confidence(self, text):
        """带置信度的预测方法"""
        max_length = int(self.max_length_input.text()) if hasattr(self, 'max_length_input') else 128

        inputs = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=max_length,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )

        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=1)
            confidence, predicted_class = torch.max(predictions, dim=1)
            predicted_class = predicted_class.item()
            confidence = confidence.item()

        predicted_label = self.label_map.get(predicted_class, "未知类别")
        return predicted_label, confidence

    def generate_analysis_chart(self):
        """生成分析图表"""
        if not self.classification_history:
            QMessageBox.information(self, '提示', '暂无分类历史数据！')
            return

        analysis_type = self.analysis_type_combo.currentText()
        self.figure.clear()

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        if analysis_type == "分类结果统计":
            self._generate_classification_stats()
        elif analysis_type == "历史趋势分析":
            self._generate_trend_analysis()
        elif analysis_type == "准确率分析":
            self._generate_accuracy_analysis()
        elif analysis_type == "类别分布图":
            self._generate_distribution_chart()

        self.canvas.draw()

    def _generate_classification_stats(self):
        """生成分类统计图表"""
        ax = self.figure.add_subplot(111)

        # 统计各类别数量
        labels = [item['predicted_label'] for item in self.classification_history]
        label_counts = pd.Series(labels).value_counts()

        # 创建柱状图
        bars = ax.bar(range(len(label_counts)), label_counts.values,
                     color=plt.cm.Set3(np.linspace(0, 1, len(label_counts))))

        ax.set_xlabel('分类类别')
        ax.set_ylabel('数量')
        ax.set_title('分类结果统计')
        ax.set_xticks(range(len(label_counts)))
        ax.set_xticklabels(label_counts.index, rotation=45, ha='right')

        # 在柱子上添加数值
        for bar, count in zip(bars, label_counts.values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                   str(count), ha='center', va='bottom')

        self.figure.tight_layout()

        # 更新统计表格
        self._update_stats_table(label_counts)

    def _generate_trend_analysis(self):
        """生成趋势分析图表"""
        ax = self.figure.add_subplot(111)

        # 按时间统计
        df = pd.DataFrame(self.classification_history)
        df['date'] = pd.to_datetime(df['timestamp']).dt.date
        daily_counts = df.groupby('date').size()

        ax.plot(daily_counts.index, daily_counts.values, marker='o', linewidth=2, markersize=6)
        ax.set_xlabel('日期')
        ax.set_ylabel('分类次数')
        ax.set_title('分类历史趋势')
        ax.grid(True, alpha=0.3)

        # 旋转x轴标签
        for label in ax.get_xticklabels():
            label.set_rotation(45)

        self.figure.tight_layout()

    def _generate_accuracy_analysis(self):
        """生成准确率分析图表"""
        ax = self.figure.add_subplot(111)

        # 计算置信度分布
        confidences = [item['confidence'] for item in self.classification_history]

        ax.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax.set_xlabel('置信度')
        ax.set_ylabel('频次')
        ax.set_title('预测置信度分布')
        ax.grid(True, alpha=0.3)

        # 添加平均置信度线
        mean_confidence = np.mean(confidences)
        ax.axvline(mean_confidence, color='red', linestyle='--',
                  label=f'平均置信度: {mean_confidence:.3f}')
        ax.legend()

        self.figure.tight_layout()

    def _generate_distribution_chart(self):
        """生成类别分布饼图"""
        ax = self.figure.add_subplot(111)

        labels = [item['predicted_label'] for item in self.classification_history]
        label_counts = pd.Series(labels).value_counts()

        # 创建饼图
        _, _, autotexts = ax.pie(label_counts.values, labels=label_counts.index,
                                autopct='%1.1f%%', startangle=90,
                                colors=plt.cm.Set3(np.linspace(0, 1, len(label_counts))))

        ax.set_title('分类结果分布')

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        self.figure.tight_layout()

    def _update_stats_table(self, label_counts):
        """更新统计表格"""
        self.stats_table.setRowCount(len(label_counts))
        self.stats_table.setColumnCount(3)
        self.stats_table.setHorizontalHeaderLabels(['类别', '数量', '占比'])

        total = sum(label_counts.values)
        for i, (label, count) in enumerate(label_counts.items()):
            self.stats_table.setItem(i, 0, QTableWidgetItem(label))
            self.stats_table.setItem(i, 1, QTableWidgetItem(str(count)))
            self.stats_table.setItem(i, 2, QTableWidgetItem(f'{count/total:.1%}'))

        # 调整列宽
        header = self.stats_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)

    def save_chart(self):
        """保存图表"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存图表", f"chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG Files (*.png);;PDF Files (*.pdf);;SVG Files (*.svg)"
        )
        if file_path:
            self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
            QMessageBox.information(self, '成功', f'图表已保存到: {file_path}')

    def import_file_and_classify(self):
        """导入文件并分类"""
        if self.model is None or self.tokenizer is None:
            self.result_label.setText('❌ 错误：模型未正确加载')
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文本文件", "",
            "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if not file_path:
            return

        try:
            self.file_result_list.clear()

            # 根据文件类型读取
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                if 'text' in df.columns:
                    lines = df['text'].dropna().tolist()
                else:
                    lines = df.iloc[:, 0].dropna().tolist()
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f if line.strip()]

            # 处理每一行
            results = []
            for idx, line in enumerate(lines):
                if len(line.strip()) > 0:
                    label, confidence = self._predict_label_with_confidence(line)
                    result_text = f"第{idx+1}行: {line[:50]}{'...' if len(line) > 50 else ''} → {label} ({confidence:.2%})"
                    self.file_result_list.addItem(result_text)
                    results.append({
                        'index': idx + 1,
                        'text': line,
                        'predicted_label': label,
                        'confidence': confidence
                    })

            # 保存结果供导出使用
            self.last_file_results = results

            self.result_label.setText(f"✅ 文件分类完成，共处理 {len(results)} 行数据。")

        except Exception as e:
            self.result_label.setText(f'❌ 文件分类出错: {str(e)}')

    def export_results(self):
        """导出分类结果"""
        if not hasattr(self, 'last_file_results') or not self.last_file_results:
            QMessageBox.information(self, '提示', '暂无可导出的结果！')
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", f"classification_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv);;Excel Files (*.xlsx)"
        )

        if file_path:
            try:
                df = pd.DataFrame(self.last_file_results)
                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                QMessageBox.information(self, '成功', f'结果已导出到: {file_path}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出失败: {str(e)}')

    # 批量处理相关方法
    def select_batch_file(self):
        """选择批量处理文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择批量处理文件", "",
            "Text Files (*.txt);;CSV Files (*.csv);;Excel Files (*.xlsx);;All Files (*)"
        )
        if file_path:
            self.batch_file_path = file_path
            self.file_path_label.setText(os.path.basename(file_path))

    def start_batch_processing(self):
        """开始批量处理"""
        if not hasattr(self, 'batch_file_path'):
            QMessageBox.warning(self, '警告', '请先选择文件！')
            return

        if self.model is None or self.tokenizer is None:
            QMessageBox.critical(self, '错误', '模型未正确加载！')
            return

        # 启动批量处理线程
        self.batch_thread = BatchProcessingThread(
            self.batch_file_path, self.model, self.tokenizer, self.label_map,
            self.include_confidence.isChecked()
        )
        self.batch_thread.progress_updated.connect(self.update_batch_progress)
        self.batch_thread.result_ready.connect(self.add_batch_result)
        self.batch_thread.finished.connect(self.batch_processing_finished)

        self.batch_process_button.setEnabled(False)
        self.stop_process_button.setEnabled(True)
        self.batch_result_table.setRowCount(0)

        self.batch_thread.start()

    def stop_batch_processing(self):
        """停止批量处理"""
        if hasattr(self, 'batch_thread') and self.batch_thread.isRunning():
            self.batch_thread.stop()

    def update_batch_progress(self, value):
        """更新批量处理进度"""
        self.progress_bar.setValue(value)

    def add_batch_result(self, result):
        """添加批量处理结果"""
        row = self.batch_result_table.rowCount()
        self.batch_result_table.insertRow(row)

        self.batch_result_table.setItem(row, 0, QTableWidgetItem(str(result['index'])))
        self.batch_result_table.setItem(row, 1, QTableWidgetItem(result['text'][:100]))
        self.batch_result_table.setItem(row, 2, QTableWidgetItem(result['predicted_label']))
        self.batch_result_table.setItem(row, 3, QTableWidgetItem(f"{result['confidence']:.2%}"))

    def batch_processing_finished(self):
        """批量处理完成"""
        self.batch_process_button.setEnabled(True)
        self.stop_process_button.setEnabled(False)
        self.progress_bar.setValue(100)
        QMessageBox.information(self, '完成', '批量处理已完成！')

    def export_batch_results(self, format_type):
        """导出批量处理结果"""
        if self.batch_result_table.rowCount() == 0:
            QMessageBox.information(self, '提示', '暂无可导出的结果！')
            return

        # 收集表格数据
        data = []
        for row in range(self.batch_result_table.rowCount()):
            data.append({
                '序号': self.batch_result_table.item(row, 0).text(),
                '文本内容': self.batch_result_table.item(row, 1).text(),
                '预测类别': self.batch_result_table.item(row, 2).text(),
                '置信度': self.batch_result_table.item(row, 3).text()
            })

        # 选择保存路径
        if format_type == 'csv':
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出CSV", f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )
        else:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel", f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

        if file_path:
            try:
                df = pd.DataFrame(data)
                if format_type == 'csv':
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                else:
                    df.to_excel(file_path, index=False)
                QMessageBox.information(self, '成功', f'结果已导出到: {file_path}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出失败: {str(e)}')

    # 设置相关方法
    def reload_model(self):
        """重新加载模型"""
        model_path = self.model_path_input.text().strip()
        if not model_path:
            QMessageBox.warning(self, '警告', '请输入模型路径！')
            return

        try:
            # 检查路径是否存在
            if not os.path.exists(model_path):
                QMessageBox.critical(self, '错误', f'模型路径不存在: {model_path}')
                return

            self.model = BertForSequenceClassification.from_pretrained(
                model_path,
                local_files_only=True
            )
            self.tokenizer = BertTokenizer.from_pretrained(
                model_path,
                local_files_only=True
            )
            self.model.eval()
            QMessageBox.information(self, '成功', '模型重新加载成功！')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'模型加载失败: {str(e)}')

    def change_theme(self, theme_name):
        """更改主题"""
        if theme_name == "深色主题":
            self.setStyleSheet(self.get_dark_theme_style())
        elif theme_name == "蓝色主题":
            self.setStyleSheet(self.get_blue_theme_style())
        else:
            self.setStyleSheet(self.get_default_theme_style())

    def get_dark_theme_style(self):
        """获取深色主题样式"""
        return """
            QMainWindow { background: #2b2b2b; color: white; }
            QGroupBox {
                background: #3c3c3c;
                border: 2px solid #555;
                color: white;
                border-radius: 8px;
            }
            QPushButton {
                background: #0d7377;
                color: white;
                border-radius: 6px;
                padding: 10px;
            }
            QPushButton:hover { background: #14a085; }
            QTextEdit, QListWidget {
                background: #3c3c3c;
                color: white;
                border: 2px solid #555;
            }
            QLabel { color: white; }
        """

    def get_blue_theme_style(self):
        """获取蓝色主题样式"""
        return """
            QMainWindow { background: #e3f2fd; }
            QGroupBox {
                background: white;
                border: 2px solid #2196f3;
                border-radius: 8px;
            }
            QPushButton {
                background: #2196f3;
                color: white;
                border-radius: 6px;
                padding: 10px;
            }
            QPushButton:hover { background: #1976d2; }
        """

    def get_default_theme_style(self):
        """获取默认主题样式"""
        return self.styleSheet()  # 返回当前样式

    def change_password(self):
        """修改密码"""
        QMessageBox.information(self, '提示', '密码修改功能待实现')

    def manage_users(self):
        """用户管理"""
        QMessageBox.information(self, '提示', '用户管理功能待实现')

    def fill_from_history(self, item):
        """从历史记录填充文本"""
        # 解析历史记录格式: [时间] 文本... → 类别
        text = item.text()
        if '→' in text:
            # 提取文本部分
            text_part = text.split('→')[0].strip()
            if '] ' in text_part:
                actual_text = text_part.split('] ', 1)[1]
                # 如果文本被截断，从历史数据中找完整文本
                for history_item in self.history:
                    if isinstance(history_item, dict) and history_item['text'].startswith(actual_text.replace('...', '')):
                        self.text_input.setText(history_item['text'])
                        return
                self.text_input.setText(actual_text.replace('...', ''))

    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(self, '确认', '确定要清空所有历史记录吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.history.clear()
            self.history_list.clear()
            self.classification_history.clear()


class BatchProcessingThread(QThread):
    """批量处理线程"""
    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(dict)
    finished = pyqtSignal()

    def __init__(self, file_path, model, tokenizer, label_map, include_confidence=True):
        super().__init__()
        self.file_path = file_path
        self.model = model
        self.tokenizer = tokenizer
        self.label_map = label_map
        self.include_confidence = include_confidence
        self.should_stop = False

    def stop(self):
        """停止处理"""
        self.should_stop = True

    def run(self):
        """运行批量处理"""
        try:
            # 读取文件
            if self.file_path.endswith('.csv'):
                df = pd.read_csv(self.file_path)
                if 'text' in df.columns:
                    lines = df['text'].dropna().tolist()
                else:
                    lines = df.iloc[:, 0].dropna().tolist()
            elif self.file_path.endswith('.xlsx'):
                df = pd.read_excel(self.file_path)
                if 'text' in df.columns:
                    lines = df['text'].dropna().tolist()
                else:
                    lines = df.iloc[:, 0].dropna().tolist()
            else:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f if line.strip()]

            total_lines = len(lines)

            for idx, line in enumerate(lines):
                if self.should_stop:
                    break

                if len(line.strip()) > 0:
                    # 预测
                    predicted_label, confidence = self._predict_with_confidence(line)

                    # 发送结果
                    result = {
                        'index': idx + 1,
                        'text': line,
                        'predicted_label': predicted_label,
                        'confidence': confidence
                    }
                    self.result_ready.emit(result)

                # 更新进度
                progress = int((idx + 1) / total_lines * 100)
                self.progress_updated.emit(progress)

        except Exception as e:
            print(f"批量处理错误: {e}")

        self.finished.emit()

    def _predict_with_confidence(self, text):
        """预测文本类别和置信度"""
        inputs = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=128,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )

        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=1)
            confidence, predicted_class = torch.max(predictions, dim=1)
            predicted_class = predicted_class.item()
            confidence = confidence.item()

        predicted_label = self.label_map.get(predicted_class, "未知类别")
        return predicted_label, confidence


def main():
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("智能文本分类系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Lab")

    window = TextClassificationUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
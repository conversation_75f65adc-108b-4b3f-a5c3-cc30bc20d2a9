import pandas as pd


def preprocess_data(file_path, output_csv):
    texts = []
    labels = []

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            # 分割文本和标签
            text, label = line.strip().split('[SEP]')
            texts.append(text.strip())
            labels.append(label.strip())  # 标签作为文本字符串

    # 创建DataFrame并保存为CSV
    df = pd.DataFrame({'text': texts, 'label': labels})
    df.to_csv(output_csv, index=False, encoding='utf-8')


# 使用示例
file_path = 'dataSet/valid_jichu_2ji_0914.txt'  # 替换为你的输入文件路径
output_csv = 'dataSet/valid_data.csv'  # 替换为你的输出CSV文件路径
preprocess_data(file_path, output_csv)

# 输出结果
print(f"数据已保存到 {output_csv}")
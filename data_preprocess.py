import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder

def preprocess_data(file_path):
    # 读取CSV文件
    df = pd.read_csv(file_path)
    
    # 创建标签编码器
    label_encoder = LabelEncoder()
    
    # 将标签转换为数字
    df['label_encoded'] = label_encoder.fit_transform(df['label'])
    
    # 保存标签映射关系
    label_mapping = dict(zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)))
    print("标签映射关系：")
    for label, num in label_mapping.items():
        print(f"{label}: {num}")
    
    # 保存处理后的数据
    output_path = file_path.replace('.csv', '_processed.csv')
    df.to_csv(output_path, index=False)
    print(f"\n处理后的数据已保存到: {output_path}")
    
    return df, label_mapping

if __name__ == "__main__":
    # 处理训练数据
    train_data, label_mapping = preprocess_data("dataSet/valid_data.csv")
    print("\n数据示例：")
    print(train_data.head()) 
<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="27">
            <item index="0" class="java.lang.String" itemvalue="thop" />
            <item index="1" class="java.lang.String" itemvalue="opencv-python" />
            <item index="2" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="3" class="java.lang.String" itemvalue="torch" />
            <item index="4" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="5" class="java.lang.String" itemvalue="cycler" />
            <item index="6" class="java.lang.String" itemvalue="numpy" />
            <item index="7" class="java.lang.String" itemvalue="ultralytics" />
            <item index="8" class="java.lang.String" itemvalue="contourpy" />
            <item index="9" class="java.lang.String" itemvalue="psutil" />
            <item index="10" class="java.lang.String" itemvalue="tqdm" />
            <item index="11" class="java.lang.String" itemvalue="fonttools" />
            <item index="12" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="13" class="java.lang.String" itemvalue="seaborn" />
            <item index="14" class="java.lang.String" itemvalue="certifi" />
            <item index="15" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="16" class="java.lang.String" itemvalue="pyqt5-tools" />
            <item index="17" class="java.lang.String" itemvalue="urllib3" />
            <item index="18" class="java.lang.String" itemvalue="pyparsing" />
            <item index="19" class="java.lang.String" itemvalue="pandas" />
            <item index="20" class="java.lang.String" itemvalue="Scrapy" />
            <item index="21" class="java.lang.String" itemvalue="pymssql" />
            <item index="22" class="java.lang.String" itemvalue="itemadapter" />
            <item index="23" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="24" class="java.lang.String" itemvalue="requests" />
            <item index="25" class="java.lang.String" itemvalue="jupyter" />
            <item index="26" class="java.lang.String" itemvalue="matplotlib" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N806" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>
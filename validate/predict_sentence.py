import torch
from transformers import BertTokenizer, BertForSequenceClassification


def predict_sentence(model_path, sentence):
    # 加载模型和分词器
    # tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
    model = BertForSequenceClassification.from_pretrained(model_path)

    # 对输入句子进行编码
    inputs = tokenizer.encode_plus(
        sentence,
        add_special_tokens=True,
        max_length=128,
        padding='max_length',
        truncation=True,
        return_tensors='pt'
    )

    input_ids = inputs['input_ids']
    attention_mask = inputs['attention_mask']

    # 进行预测
    model.eval()  # 设置模型为评估模式
    with torch.no_grad():
        outputs = model(input_ids, attention_mask=attention_mask)
        logits = outputs.logits
        predicted_class = torch.argmax(logits, dim=1).item()  # 获取预测的类别

    return predicted_class


# 使用示例
model_path = '../Module/saved_model'  # 替换为你的模型路径
sentence = "不要自动回复,自动回复"  # 替换为你要预测的句子
predicted_label = predict_sentence(model_path, sentence)
print(f"预测的标签: {predicted_label}")
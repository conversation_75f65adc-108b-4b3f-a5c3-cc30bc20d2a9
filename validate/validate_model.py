import torch
import pandas as pd
from transformers import BertTokenizer, BertForSequenceClassification
from torch.utils.data import DataLoader, Dataset

# 自定义数据集
class TextDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_len = max_len

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]
        encoding = self.tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=self.max_len,
            return_token_type_ids=False,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt',
        )
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

# 读取验证数据
def load_validation_data(csv_file):
    df = pd.read_csv(csv_file)
    texts = df['text'].tolist()
    labels = df['label'].tolist()
    return texts, labels

# 验证模型
def validate_model(model_path, valid_csv):
    # 加载验证数据
    texts, labels = load_validation_data(valid_csv)

    # 标签编码
    label_to_id = {label: idx for idx, label in enumerate(set(labels))}
    labels = [label_to_id[label] for label in labels]

    # 初始化tokenizer和数据集
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    max_len = 128
    dataset = TextDataset(texts, labels, tokenizer, max_len)
    dataloader = DataLoader(dataset, batch_size=8, shuffle=False)

    # 加载训练好的模型
    model = BertForSequenceClassification.from_pretrained(model_path)
    model.eval()  # 设置模型为评估模式

    correct_predictions = 0
    total_predictions = 0

    with torch.no_grad():  # 不计算梯度
        for batch in dataloader:
            input_ids = batch['input_ids']
            attention_mask = batch['attention_mask']
            labels = batch['labels']

            outputs = model(input_ids, attention_mask=attention_mask)
            _, preds = torch.max(outputs.logits, dim=1)

            correct_predictions += (preds == labels).sum().item()
            total_predictions += labels.size(0)

    accuracy = correct_predictions / total_predictions
    print(f"验证集准确率: {accuracy:.4f}")

# 使用示例
model_path = '../Module/bert-text-classification'  # 替换为你的模型路径
valid_csv = '../dataSet/valid_data.csv'  # 替换为你的验证数据CSV文件路径
validate_model(model_path, valid_csv)
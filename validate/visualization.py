import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 设置Mac系统中文字体
plt.rcParams['font.family'] = ['Arial Unicode MS']  # Mac系统中文字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
# 读取数据
df = pd.read_csv('../dataSet/valid_data.csv')

# 统计每个label的数量
label_counts = df['label'].value_counts()

# 创建图形
plt.figure(figsize=(15, 8))

# 绘制柱状图
sns.barplot(x=label_counts.index, y=label_counts.values)

# 设置标题和标签
plt.title('不同类别的数量统计', fontsize=14)
plt.xlabel('类别', fontsize=12)
plt.ylabel('数量', fontsize=12)

# 旋转x轴标签，以防重叠
plt.xticks(rotation=45, ha='right')

# 在柱状图上添加数值标签
for i, v in enumerate(label_counts.values):
    plt.text(i, v, str(v), ha='center', va='bottom')

# 调整布局
plt.tight_layout()

# 保存图片
plt.savefig('../dataSet/label_distribution_valid.png')
plt.close()

# 打印具体数量
print("\n各类别具体数量：")
print(label_counts)